package com.example.in_tts_hindi_pack_injector

import android.content.Context
import android.os.Build
import android.os.Environment
import android.util.Log
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.pax.dal.ISys
import java.io.File
import java.io.IOException

class TTSInstallWorker(private val appContext: Context, workerParams: WorkerParameters) :
    CoroutineWorker(appContext, workerParams) {

    companion object {
        private const val TAG = "TTSInstallWorker"
    }

    override suspend fun doWork(): Result {
        return try {
            installHindiTTSPack()
            Result.success()
        } catch (e: Exception) {
            Log.e(TAG, "Error during TTS installation", e)
            Result.failure()
        }
    }

    private fun installHindiTTSPack() {
        val iSys: ISys? = App.get().dal?.getSys()
        if (iSys == null) {
            Log.e(TAG, "ISys is null, cannot update TTS voices.")
            return
        }

        // 根据 Android 版本选择对应的文件名
        val assetFileName = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            // Android 12 (API 31)
            "hi-in_voice_sign.zip"
        } else {
            // Android 10 (API 29)
            "hi-in_sign.zip"
        }

        Log.i(TAG, "Selected asset file: $assetFileName for Android ${Build.VERSION.SDK_INT}")

        // 获取目标文件路径
        val destFile = getDestinationFile(assetFileName)
        if (destFile == null) {
            Log.e(TAG, "Failed to determine destination file path")
            return
        }

        val zipFilePath = destFile.absolutePath
        Log.i(TAG, "Target file path: $zipFilePath")

        // 检查文件是否已存在
        if (destFile.exists()) {
            Log.i(TAG, "TTS voice pack already exists at: $zipFilePath")
        } else {
            if (!copyVoiceFileFromAssets(assetFileName, destFile)) {
                return // 复制失败则返回
            }
        }

        // 确认文件状态并尝试更新
        if (!destFile.exists()) {
            Log.e(TAG, "File does not exist after copy attempt: $zipFilePath")
            return
        }

        Log.i(TAG, "File exists: ${destFile.exists()}")
        Log.i(TAG, "File can be read: ${destFile.canRead()}")
        Log.i(TAG, "File size: ${destFile.length()} bytes")

        try {
            Log.i(TAG, "Updating TTS voices with file: $zipFilePath")
            val result = iSys.updateTTSVoices(zipFilePath)
            if (result == 0) {
                Log.i(TAG, "TTS voices updated successfully.")
            } else {
                Log.e(TAG, "Failed to update TTS voices, error code: $result")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception while updating TTS voices", e)
        }
    }

    /**
     * 根据 Android 版本选择目标目录
     * Android 10: 使用应用私有外部目录（避免权限问题）
     * Android 12: 使用 /sdcard/Download 目录（系统应用可访问）
     */
    private fun getDestinationFile(fileName: String): File? {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            // Android 12及以上：使用公共下载目录
            val downloadDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
            if (downloadDir != null) {
                // 确保下载目录存在
                if (!downloadDir.exists()) {
                    if (!downloadDir.mkdirs()) {
                        Log.e(TAG, "Failed to create download directory: ${downloadDir.absolutePath}")
                        return null
                    }
                }

                // 检查目录是否可写
                if (!downloadDir.canWrite()) {
                    Log.e(TAG, "Download directory is not writable: ${downloadDir.absolutePath}")
                    return null
                }

                val targetFile = File(downloadDir, fileName)
                Log.i(TAG, "Using download directory for Android 12+: ${targetFile.absolutePath}")
                targetFile
            } else {
                Log.e(TAG, "Download directory is not available")
                null
            }
        } else {
            // Android 11及以下：使用应用私有外部目录
            val externalDir = appContext.getExternalFilesDir(null)
            if (externalDir != null) {
                val targetFile = File(externalDir, fileName)
                Log.i(TAG, "Using app private directory for Android 11-: ${targetFile.absolutePath}")
                targetFile
            } else {
                Log.e(TAG, "External files directory is not available")
                null
            }
        }
    }

    /**
     * 从 assets 复制文件到目标位置
     */
    private fun copyVoiceFileFromAssets(assetFileName: String, destFile: File): Boolean {
        Log.i(TAG, "Copying TTS voice pack from assets to: ${destFile.absolutePath}")

        return try {
            // 确保目标目录存在
            destFile.parentFile?.let { parentDir ->
                if (!parentDir.exists()) {
                    if (!parentDir.mkdirs()) {
                        Log.e(TAG, "Failed to create parent directories")
                        return false
                    }
                }
            }

            // 复制文件
            appContext.assets.open(assetFileName).use { inputStream ->
                destFile.outputStream().use { outputStream ->
                    inputStream.copyTo(outputStream)
                }
            }

            Log.i(TAG, "TTS voice pack copied successfully to: ${destFile.absolutePath}")
            Log.i(TAG, "Copied file size: ${destFile.length()} bytes")
            true

        } catch (e: IOException) {
            Log.e(TAG, "Failed to copy TTS voice pack from assets", e)
            false
        } catch (e: SecurityException) {
            Log.e(TAG, "Security exception while copying file", e)
            false
        }
    }
}